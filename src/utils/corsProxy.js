/**
 * CORS代理工具 - 提供多种方式绕过浏览器的同源策略限制
 */

/**
 * 通过公共CORS代理发送请求
 * @param {string} url 目标URL
 * @param {Object} options 请求选项
 * @returns {Promise} 请求结果
 */
export async function fetchViaCorsProxy(url, options = {}) {
  // 这里使用几个公共的CORS代理服务
  const proxyUrls = [
    `https://cors-anywhere.herokuapp.com/${url}`,
    `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`,
    `https://api.codetabs.com/v1/proxy?quest=${url}`
  ];
  
  // 尝试每个代理
  for (const proxyUrl of proxyUrls) {
    try {
      console.log(`尝试通过代理访问: ${proxyUrl}`);
      
      const response = await fetch(proxyUrl, {
        ...options,
        mode: 'cors',
        headers: {
          ...options.headers,
          'X-Requested-With': 'XMLHttpRequest'
        }
      });
      
      // 如果是第二个代理，需要特殊处理返回格式
      if (proxyUrl.includes('allorigins')) {
        const data = await response.json();
        return {
          ok: response.ok,
          status: response.status,
          data: data.contents,
          contentType: data.status.content_type
        };
      }
      
      return response;
    } catch (error) {
      console.error(`代理 ${proxyUrl} 请求失败:`, error);
      // 继续尝试下一个代理
    }
  }
  
  // 所有代理都失败了
  throw new Error('所有CORS代理请求均失败');
}

/**
 * 创建带CORS配置的Axios实例
 * @param {Object} axiosInstance Axios实例
 * @returns {Object} 配置后的Axios实例
 */
export function setupCorsForAxios(axiosInstance) {
  // 拦截请求添加CORS相关头
  axiosInstance.interceptors.request.use(config => {
    // 确保请求头存在
    config.headers = config.headers || {};
    
    // 添加CORS相关请求头
    config.headers['X-Requested-With'] = 'XMLHttpRequest';
    
    // 一些服务器可能需要这些头信息
    if (!config.headers['Access-Control-Request-Headers']) {
      config.headers['Access-Control-Request-Headers'] = 'content-type';
    }
    
    return config;
  });
  
  return axiosInstance;
}

/**
 * 创建一个JSONP请求（绕过CORS限制的另一种方式）
 * 注意：这种方法只适用于GET请求，且服务器需要支持JSONP
 * @param {string} url 目标URL
 * @param {string} callbackParam 回调参数名，默认为"callback"
 * @returns {Promise} 请求结果
 */
export function fetchViaJsonp(url, callbackParam = 'callback') {
  return new Promise((resolve, reject) => {
    // 创建唯一的回调函数名
    const callbackName = 'jsonp_callback_' + Math.round(100000 * Math.random());
    
    // 创建URL（添加回调参数）
    const requestUrl = url + 
      (url.indexOf('?') >= 0 ? '&' : '?') +
      callbackParam + '=' + callbackName;
    
    // 创建script元素
    const script = document.createElement('script');
    script.src = requestUrl;
    
    // 定义回调函数
    window[callbackName] = function(data) {
      // 清理：删除script标签和回调函数
      document.body.removeChild(script);
      delete window[callbackName];
      
      // 返回数据
      resolve(data);
    };
    
    // 处理错误
    script.onerror = function() {
      document.body.removeChild(script);
      delete window[callbackName];
      reject(new Error('JSONP请求失败'));
    };
    
    // 添加script标签到页面，开始请求
    document.body.appendChild(script);
  });
}

export default {
  fetchViaCorsProxy,
  setupCorsForAxios,
  fetchViaJsonp
}; 