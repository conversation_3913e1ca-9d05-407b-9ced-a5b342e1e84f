/**
 * 统一错误处理工具
 */

// 判断是否为HTML响应
export function isHtmlResponse(response) {
  if (typeof response === 'string' && response.includes('<!DOCTYPE html>')) {
    return true;
  }
  return false;
}

// 处理API错误
export function handleApiError(error) {
  let errorMessage = '未知错误';
  let errorCode = -1;
  
  if (error.response) {
    // 服务器返回了错误状态码
    errorCode = error.response.status;
    
    if (error.response.data) {
      if (typeof error.response.data === 'string' && isHtmlResponse(error.response.data)) {
        // HTML响应通常表示CORS或代理问题
        errorMessage = 'CORS错误或代理问题，返回了HTML页面';
      } else if (error.response.data.message) {
        // 标准API错误格式
        errorMessage = error.response.data.message;
      }
    } else {
      // 根据HTTP状态码生成错误信息
      switch (errorCode) {
        case 401:
          errorMessage = '未授权或登录已过期';
          break;
        case 403:
          errorMessage = '禁止访问';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = `请求错误 (${errorCode})`;
      }
    }
  } else if (error.request) {
    // 请求已发出但没收到响应
    errorMessage = '服务器无响应，请检查网络连接';
  } else {
    // 请求设置出错
    errorMessage = error.message || '请求设置出错';
  }
  
  // 控制台记录详细错误
  console.error('API错误:', {
    message: errorMessage,
    code: errorCode,
    originalError: error
  });
  
  return {
    message: errorMessage,
    code: errorCode,
  };
}

// 显示错误通知（可与UI组件集成）
export function showErrorNotification(errorMessage) {
  console.error('错误:', errorMessage);
  // 如果有UI通知组件，可以在这里集成
  // 例如: Message.error(errorMessage)
}

export default {
  handleApiError,
  showErrorNotification,
  isHtmlResponse
}; 