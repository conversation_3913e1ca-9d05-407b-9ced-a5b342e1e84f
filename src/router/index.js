import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import ApiDebug from '@/components/ApiDebug.vue'
import ApiDebugFetch from '@/components/ApiDebugFetch.vue'
import ApiDirectTest from '@/components/ApiDirectTest.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: Home
  },
  {
    path: '/debug',
    name: 'debug',
    component: ApiDebug
  },
  {
    path: '/debug-fetch',
    name: 'debug-fetch',
    component: ApiDebugFetch
  },
  {
    path: '/direct-test',
    name: 'direct-test',
    component: ApiDirectTest
  }
]

const router = createRouter({
  routes,
  history: createWebHistory(),
  scrollBehavior() {
    return { top: 0 }
  }
})

export default router
