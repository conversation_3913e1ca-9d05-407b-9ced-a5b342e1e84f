<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <div class="top-navigation">
      <div class="nav-header">
        <h1 class="site-title">自动化观测看板</h1>
        <div class="nav-tabs">
          <div
            class="nav-tab"
            :class="{ 'active': activeNav === 'dashboard' }"
            @click="handleNavChange('dashboard')"
          >
            测试工作台
          </div>
          <div
            class="nav-tab"
            :class="{ 'active': activeNav === 'analysis' }"
            @click="handleNavChange('analysis')"
          >
            知晓分析
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <Dashboard v-if="activeNav === 'dashboard'" />
      <div v-else-if="activeNav === 'analysis'" class="analysis-placeholder">
        <div class="placeholder-content">
          <h2>知晓分析</h2>
          <p>功能开发中...</p>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import { ref } from 'vue'
import Dashboard from '@/components/Dashboard'

export default {
  name: 'Home',
  components: {
    Dashboard
  },
  setup() {
    const activeNav = ref('dashboard')

    const handleNavChange = (nav) => {
      if (nav === 'analysis') {
        // 可以在这里添加提示信息
        console.log('知晓分析功能开发中...')
      }
      activeNav.value = nav
    }

    return {
      activeNav,
      handleNavChange
    }
  }
}
</script>


<style lang="less" scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
}

.top-navigation {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .nav-header {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;

    .site-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #2d8cf0;
    }

    .nav-tabs {
      display: flex;

      .nav-tab {
        padding: 16px 24px;
        cursor: pointer;
        font-size: 16px;
        color: #666;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          color: #2d8cf0;
        }

        &.active {
          color: #2d8cf0;
          border-bottom-color: #2d8cf0;
          font-weight: 500;
        }
      }
    }
  }
}

.content-area {
  flex: 1;
  overflow: hidden;
}

.analysis-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .placeholder-content {
    text-align: center;
    color: #666;

    h2 {
      margin-bottom: 16px;
      font-size: 24px;
      color: #333;
    }

    p {
      font-size: 16px;
      margin: 0;
    }
  }
}
</style>