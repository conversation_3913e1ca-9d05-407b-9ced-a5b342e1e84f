<template>
  <div class="edit-board">
    <Modal
      v-model="modalVisible"
      title="编辑卡片"
      width="450"
      class-name="custom-modal"
      @on-ok="handleSave"
      @on-cancel="handleCancel"
    >
      <div class="edit-form">
        <div class="form-item">
          <div class="form-label">
            卡片名
          </div>
          <Input 
            v-model="form.name" 
            placeholder="请输入卡片名称" 
            size="default"
            class="form-input"
          />
        </div>
        
        <div class="form-item">
          <div class="form-label">
            执行环境
          </div>
          <Select 
            v-model="form.ms_env_id" 
            placeholder="请选择执行环境"
            size="default"
            class="form-input"
          >
            <Option 
              v-for="env in msEnvList" 
              :key="String(Object.values(env)[0])" 
              :value="String(Object.values(env)[0])"
            >
              {{ Object.keys(env)[0] }}
            </Option>
          </Select>
        </div>
        
        <div class="form-item">
          <div class="form-label">
            飞书报告
          </div>
          <Select 
            v-model="form.feishu_report" 
            placeholder="请选择报告方式"
            size="default"
            class="form-input"
          >
            <Option 
              v-for="report in feishuReportOptions" 
              :key="String(Object.values(report)[0])" 
              :value="String(Object.values(report)[0])"
            >
              {{ Object.keys(report)[0] }}
            </Option>
          </Select>
        </div>
        
        <div class="form-item">
          <div class="form-label">
            机器人地址
          </div>
          <Input 
            v-model="form.feishu_robot" 
            placeholder="请输入机器人地址" 
            size="default"
            class="form-input"
          />
        </div>
        
        <div class="form-item">
          <div class="form-label">
            通知用户
          </div>
          <Input 
            v-model="form.feishu_user_str" 
            placeholder="请输入用户ID，多个ID使用逗号分隔" 
            size="default"
            class="form-input"
          />
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, watch, onMounted } from 'vue';
import { editBoard } from '../api/index';
import { Message } from 'view-ui-plus';

export default defineComponent({
  name: 'EditBoard',
  props: {
    // 是否显示模态框
    visible: {
      type: Boolean,
      default: false
    },
    // 环境ID
    currentEnv: {
      type: String,
      required: true
    },
    // 编辑的卡片数据
    boardData: {
      type: Object,
      required: true
    },
    // 执行环境列表
    msEnvList: {
      type: Array,
      default: () => []
    },
    // 飞书报告选项
    feishuReportOptions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:visible', 'save-success', 'cancel'],
  setup(props, { emit }) {
    // 模态框是否显示
    const modalVisible = ref(false);
    
    // 表单数据
    const form = reactive({
      name: '',
      env: '', // 当前环境
      feishu_report: '',
      ms_env_id: '',
      feishu_robot: '',
      feishu_user: [],
      feishu_user_str: ''
    });
    
    // 监听visible属性变化
    watch(() => props.visible, (newVal) => {
      modalVisible.value = newVal;
      
      if (newVal) {
        // 打开模态框时初始化表单数据
        initFormData();
      }
    });
    
    // 监听环境列表变化
    watch(() => props.msEnvList, () => {
      if (props.visible && props.msEnvList.length > 0) {
        initFormData();
      }
    });
    
    // 监听modalVisible变化，同步更新父组件的visible属性
    watch(() => modalVisible.value, (newVal) => {
      emit('update:visible', newVal);
    });
    
    // 初始化表单数据
    const initFormData = () => {
      // 编辑模式，填充数据
      form.name = props.boardData.name || '';
      form.env = props.currentEnv;
      form.feishu_report = props.boardData.feishu_report || '';
      
      // 确保ms_env_id是字符串类型
      let msEnvIdStr = '';
      if (props.boardData.ms_env_id) {
        msEnvIdStr = String(props.boardData.ms_env_id);
        console.log('原始ms_env_id值:', props.boardData.ms_env_id, '转换后:', msEnvIdStr);
      }
      form.ms_env_id = msEnvIdStr;
      
      form.feishu_robot = props.boardData.feishu_robot || '';
      form.feishu_user = props.boardData.feishu_user || [];
      form.feishu_user_str = props.boardData.feishu_user ? props.boardData.feishu_user.join(',') : '';
      
      console.log('初始化表单数据:', form);
      console.log('可用环境列表:', props.msEnvList);
    };
    
    // 保存表单
    const handleSave = async () => {
      // 表单验证
      if (!form.name) {
        Message.error('请输入卡片名称');
        return;
      }
      
      if (!form.ms_env_id) {
        Message.error('请选择执行环境');
        return;
      }
      
      // 转换用户ID字符串为数组
      form.feishu_user = form.feishu_user_str.split(',').filter(id => id.trim());
      
      try {
        await editBoard({
          name: form.name,
          scene_id: props.boardData.scene_id, // 从props中获取scene_id
          env: form.env,
          feishu_report: form.feishu_report,
          ms_env_id: form.ms_env_id,
          feishu_robot: form.feishu_robot,
          feishu_user: form.feishu_user
        });
        
        Message.success('编辑成功');
        // 通知父组件保存成功
        emit('save-success');
      } catch (error) {
        console.error('保存失败:', error);
        Message.error('保存失败，请重试');
      }
    };
    
    // 取消
    const handleCancel = () => {
      emit('cancel');
    };
    
    // 组件挂载完成
    onMounted(() => {
      console.log('EditBoard挂载完成，环境列表:', props.msEnvList);
    });
    
    return {
      modalVisible,
      form,
      handleSave,
      handleCancel
    };
  }
});
</script>

<style lang="less" scoped>
.edit-board {
  :deep(.custom-modal) {
    .ivu-modal-content {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
      
      .ivu-modal-header {
        background-color: #f8f9fa;
        padding: 12px 16px;
        
        .ivu-modal-header-inner {
          font-size: 15px;
          font-weight: 600;
          color: #333;
        }
      }
      
      .ivu-modal-body {
        padding: 16px 20px;
      }
      
      .ivu-modal-footer {
        border-top: 1px solid #f0f0f0;
        padding: 10px 16px;
        
        button {
          padding: 4px 12px;
          border-radius: 4px;
          font-weight: 500;
          font-size: 13px;
          
          &.ivu-btn-primary {
            background-color: #2d8cf0;
            border-color: #2d8cf0;
            
            &:hover {
              background-color: #5cadff;
              border-color: #5cadff;
            }
          }
          
          &.ivu-btn-text {
            color: #ed4014;
            border-color: transparent;
            
            &:hover {
              color: #f16643;
              background-color: rgba(237, 64, 20, 0.05);
            }
          }
        }
      }
    }
  }

  .edit-form {
    .form-item {
      margin-bottom: 18px;
      
      .form-label {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        font-size: 14px;
        font-weight: 800;
        color: #000;
        font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
        
        .ivu-icon {
          margin-right: 6px;
          color: #2d8cf0;
        }
      }
      
      .form-input {
        width: 100%;
        transition: all 0.3s;
        
        &:focus,
        &:hover {
          border-color: #5cadff;
          box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
        }
        
        &.ivu-select {
          :deep(.ivu-select-selection) {
            border-radius: 4px;
          }
        }
        
        &.ivu-input {
          border-radius: 4px;
        }
      }
    }
  }
}
</style> 