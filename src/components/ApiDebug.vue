<template>
  <div class="api-debug">
    <h2>API请求调试</h2>
    <div class="debug-controls">
      <Button type="primary" @click="testApi">测试请求</Button>
      <Select v-model="apiEndpoint" style="width: 200px; margin-left: 10px;">
        <Option v-for="api in apiList" :key="api.name" :value="api.name">{{ api.label }}</Option>
      </Select>
    </div>
    
    <div v-if="loading" class="debug-loading">请求中...</div>
    
    <div v-if="result" class="debug-result">
      <h3>响应结果：</h3>
      <div class="response-container">
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
    </div>
    
    <div v-if="error" class="debug-error">
      <h3>错误信息：</h3>
      <div class="error-container">
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue';
import API from '../api/index';

export default {
  name: 'ApiDebug',
  setup() {
    const apiEndpoint = ref('getBoardList');
    const loading = ref(false);
    const result = ref(null);
    const error = ref(null);
    
    const apiList = [
      { name: 'getBoardList', label: '获取看板列表' },
      { name: 'getPageConfig', label: '获取页面配置' },
      { name: 'editBoard', label: '编辑看板' },
      { name: 'runCase', label: '执行用例' }
    ];
    
    const testApi = async () => {
      loading.value = true;
      result.value = null;
      error.value = null;
      
      try {
        // 根据选择的API调用相应的方法
        let response;
        switch (apiEndpoint.value) {
          case 'getBoardList':
            response = await API.getBoardList();
            break;
          case 'getPageConfig':
            response = await API.getPageConfig();
            break;
          case 'editBoard':
            response = await API.editBoard({ test: true });
            break;
          case 'runCase':
            response = await API.runCase({ test: true });
            break;
        }
        
        result.value = response;
      } catch (err) {
        error.value = err.toString();
        // 尝试获取错误的详细信息
        if (err.response) {
          error.value += '\n\n响应状态: ' + err.response.status;
          try {
            error.value += '\n\n响应数据: ' + JSON.stringify(err.response.data);
          } catch (e) {
            error.value += '\n\n响应数据: [无法解析响应数据]';
          }
        }
      } finally {
        loading.value = false;
      }
    };
    
    return {
      apiEndpoint,
      apiList,
      loading,
      result,
      error,
      testApi
    };
  }
};
</script>

<style scoped>
.api-debug {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-controls {
  margin: 20px 0;
  display: flex;
  align-items: center;
}

.debug-result, .debug-error {
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.debug-error {
  border-color: #ff6b6b;
  background-color: #fff0f0;
}

.response-container, .error-container {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}

.error-container {
  background-color: #fff0f0;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 