<template>
  <div class="dashboard-container">
    <!-- 顶部固定区域 -->
    <div class="fixed-header">
      <!-- 顶部导航标签 -->
      <div class="tab-container">
        <div 
          class="tab-item" 
          :class="{ 'active': activeTab === 'interface' }"
          @click="handleTabChange('interface')"
        >接口自动化</div>
        <div 
          class="tab-item" 
          :class="{ 'active': activeTab === 'ui' }"
          @click="handleTabChange('ui')"
        >UI自动化</div>
      </div>
      
      <!-- 环境选择按钮组 -->
      <div class="env-selector">
        <Button 
          v-for="(value, key) in boardEnvs" 
          :key="value" 
          type="primary" 
          class="env-btn" 
          :class="{'active': currentEnv === value}"
          @click="handleEnvChange(value)"
        >{{ key }}</Button>
      </div>
    </div>
    
    <!-- 加载中蒙层 -->
    <Spin size="large" fix v-if="loading"></Spin>
    
    <!-- 可滚动的测试卡片网格 -->
    <div class="scrollable-content">
      <div class="test-card-grid">
        <DashboardCard 
          v-for="(item, index) in boardList" 
          :key="index"
          :item="item"
          @edit="openEditModal"
          @run="confirmRun"
          @report="handleOpenReport"
        />
      </div>
    </div>
    
    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <div class="button-container">
        <Button type="primary" class="action-btn" @click="handleOpenCasePage">
          更新记录
        </Button>
        <Button class="action-btn" @click="handleOpenBugPage">
          缺陷记录
        </Button>
      </div>
    </div>
    
    <!-- 编辑看板组件 -->
    <EditBoard 
      v-model:visible="showEditModal"
      :current-env="currentEnv"
      :board-data="currentEditItem"
      :ms-env-list="msEnvList"
      :feishu-report-options="feishuReportOptions"
      @save-success="handleEditSuccess"
      @cancel="handleEditCancel"
    />
    
    <!-- 执行确认弹窗 -->
    <Modal
      v-model="showRunConfirmModal"
      title="执行确认"
      class-name="run-confirm-modal"
      @on-ok="executeRun"
      @on-cancel="cancelRun"
    >
      <div class="confirm-content">
        <p>确定要执行<strong>{{ pendingRunItem?.name }}</strong>吗？</p>
        <p>当前选择环境：<strong>{{ getEnvName(currentEnv) }}</strong></p>
      </div>
    </Modal>
    
    <!-- 执行中蒙层 -->
    <div class="execution-overlay" v-if="isExecuting">
      <div class="execution-content">
        <Spin size="large" fix></Spin>
        <div class="execution-text">
          <p>正在执行：<strong>{{ pendingRunItem?.name }}</strong></p>
          <p>请稍候...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue';
import { getBoardList, getPageConfig, runCase } from '../api/index';
import { Message } from 'view-ui-plus';
import DashboardCard from './DashboardCard.vue';
import EditBoard from '../views/EditBoard.vue';

export default defineComponent({
  name: 'Dashboard',
  components: {
    DashboardCard,
    EditBoard
  },
  setup() {
    // 当前激活的tab
    const activeTab = ref('interface');
    // 当前选中的环境
    const currentEnv = ref('3'); // 默认为prod环境
    // 加载状态
    const loading = ref(false);
    // 执行中状态
    const isExecuting = ref(false);
    // 看板列表数据
    const boardList = ref([]);
    // 页面配置数据
    const pageConfig = ref({});
    // 编辑模态框显示状态
    const showEditModal = ref(false);
    // 当前编辑的卡片数据
    const currentEditItem = ref(null);
    
    // 执行确认弹窗状态
    const showRunConfirmModal = ref(false);
    // 待执行的卡片数据
    const pendingRunItem = ref(null);
    
    // 环境选项
    const boardEnvs = computed(() => {
      if (!pageConfig.value.board_envs) return {};
      const result = {};
      pageConfig.value.board_envs.forEach(item => {
        const key = Object.keys(item)[0];
        result[key] = item[key];
      });
      return result;
    });
    
    // 执行环境列表
    const msEnvList = computed(() => {
      return pageConfig.value.ms_env || [];
    });
    
    // 飞书报告选项
    const feishuReportOptions = computed(() => {
      return pageConfig.value.feishu_report || [];
    });
    
    // 更新记录链接
    const casePage = computed(() => {
      if (!pageConfig.value.page) return '';
      const interfacePage = pageConfig.value.page.find(item => item.interface_page);
      return interfacePage && interfacePage.interface_page[0]?.case_page || '';
    });
    
    // 缺陷记录链接
    const bugPage = computed(() => {
      if (!pageConfig.value.page) return '';
      const interfacePage = pageConfig.value.page.find(item => item.interface_page);
      return interfacePage && interfacePage.interface_page[1]?.bug_page || '';
    });
    
    // 获取环境名称
    const getEnvName = (envId) => {
      for (const key in boardEnvs.value) {
        if (boardEnvs.value[key] === envId) {
          return key;
        }
      }
      return '未知环境';
    };
    
    // 初始化加载数据
    const initData = async () => {
      loading.value = true;
      try {
        // 并行请求，提高页面加载速度
        const [boardRes, configRes] = await Promise.all([
          getBoardList(currentEnv.value),
          getPageConfig(activeTab.value === 'interface' ? '0' : '1')
        ]);
        
        boardList.value = boardRes.data || [];
        pageConfig.value = configRes.data || {};
      } catch (error) {
        console.error('初始化数据失败:', error);
        Message.error('加载数据失败，请刷新页面重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 切换Tab
    const handleTabChange = (tab) => {
      if (tab === 'ui') {
        Message.info('功能开发中...');
        return;
      }
      
      activeTab.value = tab;
      // 重新加载数据
      initData();
    };
    
    // 切换环境
    const handleEnvChange = (env) => {
      currentEnv.value = env;
      // 刷新列表
      refreshBoardList();
    };
    
    // 刷新看板列表
    const refreshBoardList = async () => {
      loading.value = true;
      try {
        const res = await getBoardList(currentEnv.value);
        boardList.value = res.data || [];
      } catch (error) {
        console.error('刷新列表失败:', error);
        Message.error('刷新列表失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 打开编辑模态框
    const openEditModal = (item) => {
      if (!item) return; // 不再支持没有item的情况
      currentEditItem.value = item;
      showEditModal.value = true;
    };
    
    // 编辑成功回调
    const handleEditSuccess = () => {
      showEditModal.value = false;
      // 刷新列表
      refreshBoardList();
    };
    
    // 编辑取消回调
    const handleEditCancel = () => {
      showEditModal.value = false;
      currentEditItem.value = null;
    };
    
    // 确认执行用例
    const confirmRun = (item) => {
      // 如果卡片正在执行中，直接返回，不显示确认弹窗
      if (item.exec_status === 'PENDING' && item.status === '-' ||
          item.exec_status === 'RUNNING' && item.status === 'PENDING') {
        Message.warning(`${item.name} 正在执行中，请稍候...`);
        return;
      }
      
      pendingRunItem.value = item;
      showRunConfirmModal.value = true;
    };
    
    // 执行确认
    const executeRun = async () => {
      if (!pendingRunItem.value) return;
      
      // 立即显示执行中蒙层
      isExecuting.value = true;
      
      try {
        await runCase({
          scene_id: pendingRunItem.value.scene_id,
          env: currentEnv.value
        });
        
        Message.success('执行成功');
        // 刷新列表
        refreshBoardList();
      } catch (error) {
        console.error('执行失败:', error);
        Message.error('执行失败，请重试');
      } finally {
        setTimeout(() => {
          isExecuting.value = false;
          pendingRunItem.value = null;
        }, 500); // 延迟关闭蒙层，让用户有足够时间感知状态变化
      }
    };
    
    // 取消执行
    const cancelRun = () => {
      pendingRunItem.value = null;
    };
    
    // 打开报告
    const handleOpenReport = (item) => {
      window.open(item.report_url, '_blank');
    };
    
    // 打开更新记录
    const handleOpenCasePage = () => {
      if (casePage.value) {
        window.open(casePage.value, '_blank');
      } else {
        Message.warning('未配置更新记录页面');
      }
    };
    
    // 打开缺陷记录
    const handleOpenBugPage = () => {
      if (bugPage.value) {
        window.open(bugPage.value, '_blank');
      } else {
        Message.warning('未配置缺陷记录页面');
      }
    };
    
    // 组件挂载后初始化数据
    onMounted(() => {
      initData();
    });

    return {
      activeTab,
      currentEnv,
      loading,
      isExecuting,
      boardList,
      pageConfig,
      boardEnvs,
      msEnvList,
      feishuReportOptions,
      showEditModal,
      currentEditItem,
      showRunConfirmModal,
      pendingRunItem,
      casePage,
      bugPage,
      getEnvName,
      handleTabChange,
      handleEnvChange,
      openEditModal,
      handleEditSuccess,
      handleEditCancel,
      confirmRun,
      executeRun,
      cancelRun,
      handleOpenReport,
      handleOpenCasePage,
      handleOpenBugPage
    };
  }
});
</script>

<style lang="less" scoped>
.dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #f0f2f5;
  overflow: hidden;
  
  .fixed-header {
    background-color: #f0f2f5;
    z-index: 10;
    padding: 20px 20px 0;
  }
  
  .tab-container {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
    
    .tab-item {
      padding: 10px 20px;
      cursor: pointer;
      position: relative;
      
      &.active {
        color: #2d8cf0;
        font-weight: bold;
        
        &:after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #2d8cf0;
        }
      }
    }
  }
  
  .env-selector {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
    
    .env-btn {
      margin-left: 5px;
      
      &.active {
        background-color: #2d8cf0;
        color: white;
      }
    }
  }
  
  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
    padding-bottom: 15px;
    
    /* 确保有足够空间显示卡片悬浮效果 */
    .test-card-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 16px;
      padding-bottom: 20px;
      padding-top: 5px;
      
      /* 给卡片四周添加间距，防止悬浮效果被裁剪 */
      & > * {
        margin: 3px;
      }
    }
  }
  
  .bottom-actions {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f0f2f5;
    padding: 15px 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: flex-end;
    z-index: 10;
    
    .button-container {
      display: flex;
      gap: @spacing-large;
    }
    
    .action-btn {
      min-width: 120px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: @border-radius-base;
      transition: all @transition-time-fast ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      i {
        margin-right: @spacing-small;
      }
    }
  }
  
  /* 执行确认弹窗样式 */
  :deep(.run-confirm-modal) {
    .ivu-modal-content {
      border-radius: 8px;
      overflow: hidden;
      
      .ivu-modal-header {
        background-color: #f8f9fa;
        padding: 12px 16px;
        
        .ivu-modal-header-inner {
          font-size: 15px;
          font-weight: 600;
          color: #333;
        }
      }
      
      .ivu-modal-body {
        padding: 20px;
      }
      
      .ivu-modal-footer {
        border-top: 1px solid #f0f0f0;
        padding: 10px 16px;
        
        button {
          &.ivu-btn-primary {
            background-color: #2d8cf0;
            border-color: #2d8cf0;
          }
          
          &.ivu-btn-text {
            color: #ed4014;
          }
        }
      }
    }
  }
  
  .confirm-content {
    text-align: center;
    font-size: 14px;
    line-height: 1.8;
    
    p {
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    strong {
      font-weight: bold;
      color: #2d8cf0;
    }
  }
  
  /* 执行中蒙层样式 */
  .execution-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    
    .execution-content {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      
      .execution-text {
        margin-top: 10px;
        font-size: 14px;
        line-height: 1.8;
        
        p {
          margin-bottom: 10px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        strong {
          font-weight: bold;
          color: #2d8cf0;
        }
      }
    }
  }
}
</style> 