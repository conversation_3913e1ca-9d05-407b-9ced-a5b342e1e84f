<template>
  <div 
    class="test-card" 
    :class="{
      'running': item.exec_status === 'RUNNING' && item.status ==='PENDING',
      'success': item.exec_status === 'COMPLETED' && item.status === 'SUCCESS', 
      'failed': item.exec_status === 'COMPLETED' && item.status === 'ERROR'
    }"
  >
    <div class="card-inner">
      <div class="card-header">
        <div class="card-title" :title="item.name">{{ item.name }}</div>
        <div class="card-actions" @click="$emit('edit', item)" title="编辑">
          <span class="btn-text edit-text">编辑</span>
        </div>
      </div>
      <div class="card-body">
        <div class="status-item">
          <span class="label">最近执行：</span>
          <span class="value">{{ item.end_time|| '-'}}</span>
        </div>
        <div class="status-item">
          <span class="label">场景数：</span>
          <span class="value">{{ item.scene_num || '-' }}</span>
        </div>
        <div class="status-item">
          <span class="label">通过：</span>
          <span class="value">{{ item.success_count || '-' }}</span>
        </div>
        <div class="status-item">
          <span class="label">失败：</span>
          <span class="value" :class="{ 'error-count': item.error_count > 0 }">{{ item.error_count || '-' }}</span>
        </div>
      </div>
      <div class="card-footer">
        <div 
          class="action-btn play-btn" 
          :class="{ 'disabled': item.exec_status === 'RUNNING' && item.status ==='PENDING' || item.exec_status === 'PENDING' && item.status === '-' }"
          @click="handleRun"
          :title="(item.exec_status === 'RUNNING' && item.status ==='PENDING' || item.exec_status === 'PENDING' && item.status === '-') ? '执行中...' : '执行'"
        >
          <span class="btn-text">执行</span>
        </div>
        <div 
          class="action-btn report-btn" 
          :class="{ 'disabled': !item.report_url }"
          @click="handleReport"
          :title="!item.report_url ? '暂无报告' : '查看报告'"
        >
          <span class="btn-text">报告</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'DashboardCard',
  props: {
    // 卡片数据
    item: {
      type: Object,
      required: true
    }
  },
  emits: ['edit', 'run', 'report'],
  setup(props, { emit }) {
    // 执行用例
    const handleRun = () => {
      // 如果正在执行中，禁止点击
      if (props.item.exec_status === 'PENDING' && props.item.status === '-' ||
          props.item.exec_status === 'RUNNING' && props.item.status === 'PENDING') {
        return;
      }
      
      emit('run', props.item);
    };
    
    // 查看报告
    const handleReport = () => {
      // 如果没有报告URL，禁止点击
      if (!props.item.report_url) {
        return;
      }
      
      emit('report', props.item);
    };
    
    return {
      handleRun,
      handleReport
    };
  }
});
</script>

<style lang="less" scoped>
.test-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: @shadow-card;
  overflow: visible;
  border: 1px solid @border-color-base;
  transition: all @transition-time ease;
  position: relative;
  z-index: 1;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: @shadow-card-hover;
    z-index: 2;
  }
  
  /* 内容容器，保持overflow:hidden */
  .card-inner {
    overflow: hidden;
    border-radius: 8px;
  }
  
  &.running {
    border-color: @warning-color;
  }
  
  &.success {
    border-color: @success-color;
  }
  
  &.failed {
    border-color: @error-color;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 14px;
    border-bottom: 1px solid @border-color-split;
    
    .card-title {
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 85%;
    }
    
    .card-actions {
      cursor: pointer;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all @transition-time-fast ease;
      
      .edit-text {
        color: @success-color;
        font-weight: 500;
        font-size: 13px;
      }
      
      &:hover {
        .edit-text {
          color: darken(@success-color, 10%);
        }
      }
    }
  }
  
  .card-body {
    padding: 12px 14px;
    
    .status-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 13px;
      
      .label {
        width: 70px;
        color: @text-color-secondary;
      }
      
      .value {
        font-weight: 500;
        
        &.error-count {
          color: @error-color;
          font-weight: bold;
        }
      }
    }
  }
  
  .card-footer {
    display: flex;
    
    .action-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px;
      cursor: pointer;
      transition: all @transition-time-fast ease;
      background-color: @background-color-light;
      gap: 6px;
      color: @text-color;
      
      .btn-text {
        font-size: 13px;
      }
      
      &:first-child {
        border-right: 1px solid @border-color-split;
      }
      
      &.play-btn:hover:not(.disabled) {
        background-color: rgba(76, 175, 80, 0.15);
        color: @success-color;
      }
      
      &.report-btn:hover:not(.disabled) {
        background-color: rgba(45, 140, 240, 0.15);
        color: @primary-color;
      }
      
      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }
}
</style> 