<template>
  <div class="api-debug">
    <h2>API直连测试</h2>
    <div class="debug-controls">
      <Button type="primary" @click="testApi">测试直连请求</Button>
      <Button type="warning" @click="testFetch" style="margin-left: 10px;">测试Fetch请求</Button>
      <Button type="success" @click="testCorsProxy" style="margin-left: 10px;">测试CORS代理</Button>
      <Select v-model="apiEndpoint" style="width: 200px; margin-left: 10px;">
        <Option v-for="api in apiList" :key="api.value" :value="api.value">{{ api.label }}</Option>
      </Select>
    </div>
    
    <div class="url-display">
      <h3>请求URL:</h3>
      <div class="url-text">{{ currentUrl }}</div>
    </div>
    
    <div v-if="loading" class="debug-loading">请求中...</div>
    
    <div v-if="result" class="debug-result">
      <h3>响应结果：</h3>
      <div class="response-container">
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
    </div>
    
    <div v-if="error" class="debug-error">
      <h3>错误信息：</h3>
      <div class="error-container">
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import API from '../api';
import axios from 'axios';
import { fetchViaCorsProxy, fetchViaJsonp } from '../utils/corsProxy';

export default {
  name: 'ApiDirectTest',
  setup() {
    const apiEndpoint = ref('/msboard/interface/list');
    const loading = ref(false);
    const result = ref(null);
    const error = ref(null);
    
    const apiList = [
      { value: '/msboard/interface/list', label: '获取看板列表' },
      { value: '/msboard/config_page', label: '获取页面配置' },
      { value: '/msboard/edit', label: '编辑看板' },
      { value: '/msboard/run', label: '执行用例' }
    ];
    
    const currentUrl = computed(() => {
      return `http://*************:8000${apiEndpoint.value}`;
    });
    
    // 使用封装的API测试
    const testApi = async () => {
      loading.value = true;
      result.value = null;
      error.value = null;
      
      try {
        // 根据选择的API调用相应的方法
        let response;
        switch (apiEndpoint.value) {
          case '/msboard/interface/list':
            response = await API.getBoardList();
            break;
          case '/msboard/config_page':
            response = await API.getPageConfig();
            break;
          case '/msboard/edit':
            response = await API.editBoard({ test: true });
            break;
          case '/msboard/run':
            response = await API.runCase({ test: true });
            break;
        }
        
        result.value = response;
      } catch (err) {
        error.value = JSON.stringify(err, null, 2);
        console.error('API测试错误:', err);
      } finally {
        loading.value = false;
      }
    };
    
    // 使用原生fetch测试
    const testFetch = async () => {
      loading.value = true;
      result.value = null;
      error.value = null;
      
      try {
        const url = currentUrl.value;
        const params = apiEndpoint.value.includes('list') ? '?env=3' : 
                      apiEndpoint.value.includes('config_page') ? '?scene_type=0' : '';
        
        const isGet = !apiEndpoint.value.includes('edit') && !apiEndpoint.value.includes('run');
        
        let response;
        if (isGet) {
          // GET请求
          response = await fetch(`${url}${params}`, {
            method: 'GET',
            mode: 'cors', // 启用CORS
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type'
            }
          });
        } else {
          // POST请求
          response = await fetch(url, {
            method: 'POST',
            mode: 'cors', // 启用CORS
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type'
            },
            body: JSON.stringify({ test: true })
          });
        }
        
        // 检查响应是否为JSON
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const data = await response.json();
          result.value = data;
        } else {
          // 如果不是JSON，则作为文本处理
          const text = await response.text();
          result.value = { 
            __non_json_response: true,
            statusCode: response.status,
            statusText: response.statusText,
            responseText: text.length > 500 ? text.substring(0, 500) + "..." : text
          };
        }
      } catch (err) {
        console.error('Fetch请求出错:', err);
        error.value = err.toString();
      } finally {
        loading.value = false;
      }
    };
    
    // 使用CORS代理测试
    const testCorsProxy = async () => {
      loading.value = true;
      result.value = null;
      error.value = null;
      
      try {
        const url = currentUrl.value;
        const params = apiEndpoint.value.includes('list') ? '?env=3' : 
                      apiEndpoint.value.includes('config_page') ? '?scene_type=0' : '';
        
        const isGet = !apiEndpoint.value.includes('edit') && !apiEndpoint.value.includes('run');
        
        let response;
        if (isGet) {
          // 尝试通过CORS代理发送GET请求
          response = await fetchViaCorsProxy(`${url}${params}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
        } else {
          // 尝试通过CORS代理发送POST请求
          response = await fetchViaCorsProxy(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ test: true })
          });
        }
        
        // 如果是代理返回的封装响应
        if (response.data !== undefined) {
          try {
            // 尝试解析内容为JSON
            const jsonData = JSON.parse(response.data);
            result.value = jsonData;
          } catch(e) {
            // 不是JSON，显示文本内容
            result.value = {
              __non_json_response: true,
              statusCode: response.status,
              responseText: response.data.length > 500 ? response.data.substring(0, 500) + "..." : response.data
            };
          }
        } else {
          // 常规响应
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const data = await response.json();
            result.value = data;
          } else {
            // 如果不是JSON，则作为文本处理
            const text = await response.text();
            result.value = { 
              __non_json_response: true,
              statusCode: response.status,
              statusText: response.statusText,
              responseText: text.length > 500 ? text.substring(0, 500) + "..." : text
            };
          }
        }
      } catch (err) {
        console.error('CORS代理请求出错:', err);
        error.value = err.toString();
      } finally {
        loading.value = false;
      }
    };
    
    return {
      apiEndpoint,
      apiList,
      loading,
      result,
      error,
      testApi,
      testFetch,
      testCorsProxy,
      currentUrl
    };
  }
};
</script>

<style scoped>
.api-debug {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-controls {
  margin: 20px 0;
  display: flex;
  align-items: center;
}

.url-display {
  margin: 20px 0;
  padding: 10px;
  background: #f7f7f7;
  border-radius: 4px;
}

.url-text {
  font-family: monospace;
  padding: 5px;
  background: #eaeaea;
}

.debug-result, .debug-error {
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.debug-error {
  border-color: #ff6b6b;
  background-color: #fff0f0;
}

.response-container, .error-container {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}

.error-container {
  background-color: #fff0f0;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 