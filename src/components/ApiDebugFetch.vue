<template>
  <div class="api-debug">
    <h2>Fetch API请求调试</h2>
    <div class="debug-controls">
      <Button type="primary" @click="testApi">测试请求</Button>
      <Select v-model="apiEndpoint" style="width: 200px; margin-left: 10px;">
        <Option v-for="api in apiList" :key="api.value" :value="api.value">{{ api.label }}</Option>
      </Select>
    </div>
    
    <div class="url-display">
      <h3>请求URL:</h3>
      <div class="url-text">{{ currentUrl }}</div>
    </div>
    
    <div v-if="loading" class="debug-loading">请求中...</div>
    
    <div v-if="result" class="debug-result">
      <h3>响应结果：</h3>
      <div class="response-container">
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
    </div>
    
    <div v-if="error" class="debug-error">
      <h3>错误信息：</h3>
      <div class="error-container">
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';

export default {
  name: 'ApiDebugFetch',
  setup() {
    const apiEndpoint = ref('/msboard/interface/list');
    const loading = ref(false);
    const result = ref(null);
    const error = ref(null);
    
    const apiList = [
      { value: '/msboard/interface/list', label: '获取看板列表' },
      { value: '/msboard/config_page', label: '获取页面配置' },
      { value: '/msboard/edit', label: '编辑看板' },
      { value: '/msboard/run', label: '执行用例' }
    ];
    
    const currentUrl = computed(() => {
      return `/api${apiEndpoint.value}`;
    });
    
    const testApi = async () => {
      loading.value = true;
      result.value = null;
      error.value = null;
      
      try {
        const url = `/api${apiEndpoint.value}`;
        const params = apiEndpoint.value.includes('list') ? '?env=3' : 
                       apiEndpoint.value.includes('config_page') ? '?scene_type=0' : '';
        
        const isGet = !apiEndpoint.value.includes('edit') && !apiEndpoint.value.includes('run');
        
        let response;
        if (isGet) {
          // GET请求
          response = await fetch(`${url}${params}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
        } else {
          // POST请求
          response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ test: true })
          });
        }
        
        // 检查响应是否为JSON
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const data = await response.json();
          result.value = data;
        } else {
          // 如果不是JSON，则作为文本处理
          const text = await response.text();
          result.value = { 
            __non_json_response: true,
            statusCode: response.status,
            statusText: response.statusText,
            responseText: text.length > 500 ? text.substring(0, 500) + "..." : text
          };
        }
      } catch (err) {
        console.error('请求出错:', err);
        error.value = err.toString();
      } finally {
        loading.value = false;
      }
    };
    
    return {
      apiEndpoint,
      apiList,
      loading,
      result,
      error,
      testApi,
      currentUrl
    };
  }
};
</script>

<style scoped>
.api-debug {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-controls {
  margin: 20px 0;
  display: flex;
  align-items: center;
}

.url-display {
  margin: 20px 0;
  padding: 10px;
  background: #f7f7f7;
  border-radius: 4px;
}

.url-text {
  font-family: monospace;
  padding: 5px;
  background: #eaeaea;
}

.debug-result, .debug-error {
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.debug-error {
  border-color: #ff6b6b;
  background-color: #fff0f0;
}

.response-container, .error-container {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}

.error-container {
  background-color: #fff0f0;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 