import { ref } from 'vue'
import axios from 'axios'
import { Message } from 'view-ui-plus'

// 创建全局加载状态
export const globalLoading = ref(false)

// 配置axios
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 10000,
  withCredentials: false
})

// 请求拦截器
apiClient.interceptors.request.use(config => {
  globalLoading.value = true
  return config
}, error => {
  globalLoading.value = false
  return Promise.reject(error)
})

// 响应拦截器
apiClient.interceptors.response.use(response => {
  globalLoading.value = false
  return response
}, error => {
  globalLoading.value = false
  Message.error('网络错误，请稍后重试')
  return Promise.reject(error)
})

export { apiClient } 