{"name": "view-ui-project-vite", "version": "1.0.0", "description": "a template project for vue3, vue-router, vuex, ViewUIPlus and vite.", "scripts": {"serve": "npm run dev", "dev": "vite serve", "build": "vite build", "build:staging": "vite build --mode=staging", "preview": "npm run build && vite preview", "preview:staging": "npm run build:staging && vite preview --mode=staging", "lint": "eslint ./src --ext .js,.vue", "lint:fix": "eslint ./src --ext .js,.vue --fix", "prepare": "husky install", "proxy": "node server.js"}, "dependencies": {"axios": "^1.8.4", "cors": "^2.8.5", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "view-ui-plus": "^1.3.0", "vue": "^3.2.25", "vue-router": "^4.0.14", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.5", "eslint": "^8.14.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-vue": "^8.7.1", "husky": "^7.0.4", "less": "^4.0.0", "less-loader": "^10.2.0", "mockjs": "^1.1.0", "vite": "^6.3.1", "vite-plugin-html": "^3.2.0"}, "engines": {"node": ">= 12.0.0", "npm": ">= 6.9.0"}, "browserslist": ["> 1%", "last 2 versions"]}